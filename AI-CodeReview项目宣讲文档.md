# AI-CodeReview 项目宣讲文档

## 📋 目录
1. [项目概述](#项目概述)
2. [二次开发核心功能](#二次开发核心功能)
3. [核心功能总结](#核心功能总结)
4. [如何接入使用](#如何接入使用)
5. [技术优势](#技术优势)
6. [部署方案](#部署方案)

---

## 🎯 项目概述

AI-CodeReview 是一个基于大模型的自动化代码审查工具，专为技术团队打造，能够在代码合并或提交时自动进行智能化审查，显著提升代码质量和开发效率。

### 🌟 项目亮点
- **全栈项目支持**：覆盖前后端20+种文件类型，智能识别项目类型并应用相应代码规范
- **审查结果简洁有效**：通过优化的提示词模板，确保审查结果简洁且有重点，避免信息冗余
- **飞书增强通知**：与JARVIS账号体系打通，智能获取群组配置，精准艾特相关开发人员
- **架构设计易于扩展**：模块化设计，统一的LLM客户端工厂，轻松接入新的AI服务商
- **Python开发友好**：全栈Python技术栈，前后端开发都能快速上手维护和扩展

---

## 🚀 二次开发核心功能

### 项目基础架构说明

本项目的基础架构来源于开源项目 [AI-Codereview-Gitlab](https://github.com/sunmh207/AI-Codereview-Gitlab)，这是一个成熟的基于大模型的自动化代码审查工具。我们在此开源项目的基础上，结合团队实际需求进行了深度的二次开发和功能增强。

**开源项目原有特性：**
- Flask + Streamlit 双服务架构
- 多AI模型支持（DeepSeek、OpenAI、ZhipuAI等）
- GitLab/GitHub Webhook集成
- 基础的消息推送功能

**我们的二次开发重点：**
在保留原有架构优势的基础上，我们针对团队协作和代码质量管控的实际需求，进行了以下5个方面的核心功能增强：

### 1. 提示词多轮优化

#### 1.1 Jinja2模板引擎集成
我们引入了Jinja2模板引擎，实现了动态提示词渲染，使CodeReview结果更加准确简洁。

**核心实现：**
```python
def _load_prompts(self, prompt_key: str, style="professional", team_standards="") -> Dict[str, Any]:
    """加载提示词配置"""
    with open(prompt_templates_file, "r", encoding="utf-8") as file:
        prompts = yaml.safe_load(file).get(prompt_key, {})
        
        # 使用Jinja2渲染模板
        def render_template(template_str: str) -> str:
            return Template(template_str).render(style=style, team_standards=team_standards)
        
        system_prompt = render_template(prompts["system_prompt"])
        user_prompt = render_template(prompts["user_prompt"])
```

#### 1.2 优化效果
- **精准性提升**：通过多轮调优，减少误报和漏报
- **简洁性增强**：避免冗余描述，突出关键问题
- **风格化定制**：支持4种不同的审查风格

### 2. 后端文件类型支持扩展

#### 2.1 全面的文件类型覆盖
扩展了支持的文件类型，涵盖前后端主流技术栈：

```bash
# 支持的文件类型
SUPPORTED_EXTENSIONS=.js,.jsx,.ts,.tsx,.vue,.html,.css,.scss,.less,.json,.yaml,.yml,.md,.java,.py,.go,.php,.cs,.rs,.rb,.sql,.sh,.dockerfile
```

#### 2.2 智能文件识别
- **前端技术栈**：JavaScript、TypeScript、Vue、React、CSS预处理器
- **后端技术栈**：Java、Python、Go、PHP、C#、Rust、Ruby
- **配置文件**：YAML、JSON、SQL、Shell脚本、Dockerfile

### 3. 自动加载代码规范

#### 3.1 精简版代码规范
我们为前后端分别制定了精简版代码规范，自动集成到审查流程中：

**前端代码规范要点：**
- 命名规范：camelCase变量、PascalCase组件
- 代码格式：ESLint + Prettier自动格式化
- Vue/React最佳实践
- TypeScript类型安全

**后端代码规范要点：**
- 命名规范：清晰简洁，避免冗余
- 错误处理：强制返回error，禁止panic
- 并发编程：goroutine管理和Context使用
- 数据库规范：连接池配置、索引优化

#### 3.2 动态规范加载
```python
# 自动加载团队代码规范
{% if team_standards %}
### 团队代码规范：
{{ team_standards }}
{% endif %}
```

### 4. JARVIS集成与飞书消息增强

#### 4.1 JARVIS用户体系打通
实现了GitLab账号体系与JARVIS用户体系的无缝对接：

```python
class JarvisClient:
    def get_user_by_email(self, email: str) -> Optional[Dict[str, Any]]:
        """根据邮箱获取JARVIS用户信息"""
        
    def get_groups_by_user_email(self, email: str) -> Optional[Dict[str, Any]]:
        """根据用户邮箱获取AICR群配置"""
        
    def get_groups_by_project_id(self, project_id: str) -> Optional[List[Dict[str, Any]]]:
        """根据项目ID获取对应的AICR群配置"""
```

#### 4.2 智能分群发送
- **项目群优先**：优先查找项目专属群，提高消息精准度
- **用户群兜底**：项目群不存在时，自动使用用户配置群
- **多群支持**：支持同时推送到多个相关群组

#### 4.3 精准艾特功能
```python
def send_code_review_message(self, webhook_url: str, at_users: List[str] = None):
    # 构建艾特用户的文本
    at_text = ""
    if at_users:
        at_mentions = []
        for user_id in at_users:
            if user_id:
                at_mentions.append(f"<at id={user_id}></at>")
        if at_mentions:
            at_text = f"\n\n👥 相关人员: {' '.join(at_mentions)}"
```

### 5. CI/CD自动化部署

#### 5.1 多环境部署支持
实现了完整的CI/CD流水线，支持测试环境和生产环境的自动化部署：

**测试环境部署：**
- 触发条件：`feat/supportAICR` 分支推送
- 镜像标签：`${CI_COMMIT_SHORT_SHA}_fe_test`
- 自动部署到Kubernetes测试集群

**生产环境部署：**
- 触发条件：`frontend` 分支 + Git Tag
- 镜像标签：`${CI_COMMIT_TAG}_fe`
- 自动部署到Kubernetes生产集群

#### 5.2 容器化架构
```dockerfile
# 多阶段构建，支持app和worker分离部署
FROM docker.yc345.tv/mirror/python:3.10-slim AS base
# ... 基础环境配置

FROM base AS app
# Flask API + Streamlit Dashboard

FROM base AS worker  
# Redis Queue Worker
```

#### 5.3 服务监控
- **Supervisord进程管理**：确保服务高可用
- **健康检查**：自动重启异常服务
- **日志集中管理**：结构化日志输出

---

## 🎯 核心功能总结

### 智能代码审查
- **多维度检测**：功能正确性、安全性、性能优化、最佳实践
- **风格化输出**：专业、讽刺、温和、幽默四种风格
- **精准评分**：基于团队规范的量化评分机制

### 消息推送系统
- **多平台支持**：钉钉、企业微信、飞书
- **智能分群**：基于JARVIS配置的自动分群
- **精准艾特**：自动艾特相关开发人员

### 可视化Dashboard
- **审查记录**：完整的Code Review历史
- **统计分析**：项目统计、开发者统计
- **趋势分析**：代码质量趋势图表

### 自动化集成
- **GitLab Webhook**：自动触发审查流程
- **CI/CD部署**：一键部署到多环境
- **队列处理**：Redis Queue异步处理

---

## 📱 如何接入使用

### 1. 飞书群配置

参考 `jarvis-fe` 项目的 `/src/pages/aicr` 功能进行飞书群配置：

#### 1.1 配置步骤
1. **登录JARVIS管理后台**
2. **进入AICR配置页面** (`/src/pages/aicr`)
3. **添加项目群配置**：
   - 项目ID：GitLab项目ID
   - 飞书群Webhook：群机器人Webhook地址
   - 群名称：便于管理的群组名称

#### 1.2 用户群配置
1. **个人群配置**：为个人配置默认接收群
2. **项目群优先**：系统优先使用项目专属群
3. **兜底机制**：项目群不存在时使用个人群

### 2. GitLab项目接入

#### 2.1 配置Webhook
在GitLab项目设置中添加Webhook：
- **URL**：`http://your-server-ip:5001/review/webhook`
- **触发事件**：Push Events + Merge Request Events
- **Secret Token**：GitLab Access Token（可选）

#### 2.2 环境变量配置
```bash
# 大模型配置
LLM_PROVIDER=deepseek
DEEPSEEK_API_KEY=your_api_key

# JARVIS集成
JARVIS_INTEGRATION_ENABLED=1
JARVIS_BASE_URL=https://jarvis-test-api.yc345.tv/v2

# 飞书配置
FEISHU_ENABLED=1
FEISHU_WEBHOOK_URL=your_feishu_webhook

# GitLab配置
GITLAB_ACCESS_TOKEN=your_gitlab_token
```

### 3. 团队规范定制

#### 3.1 代码规范配置
- **前端规范**：`conf/frontend_code_standard_zh.md`
- **后端规范**：`conf/backend_code_standard_zh.md`
- **自动加载**：系统自动根据文件类型加载对应规范

#### 3.2 审查风格选择
```bash
# 可选风格：professional | sarcastic | gentle | humorous
REVIEW_STYLE=gentle
```

---

## 🔧 技术优势

### 1. 架构优势
- **微服务架构**：API服务与Worker服务分离
- **异步处理**：Redis Queue支持高并发
- **容器化部署**：Docker + Kubernetes云原生架构

### 2. 扩展性优势
- **多模型支持**：轻松切换不同AI模型
- **插件化设计**：支持自定义审查规则
- **多语言支持**：覆盖主流编程语言

### 3. 可靠性优势
- **容错机制**：多重兜底保障
- **监控告警**：完善的日志和监控
- **自动恢复**：服务异常自动重启

---

## 🚀 部署方案

### Docker Compose 一键部署
```bash
# 克隆项目
git clone https://gitlab.yc345.tv/ios/tools/AI-CodeReview.git
cd AI-CodeReview

# 配置环境变量
cp conf/.env.dist conf/.env
# 编辑 conf/.env 文件

# 启动服务
docker-compose up -d
```

### Kubernetes 生产部署
- **自动化CI/CD**：GitLab CI自动构建部署
- **多环境支持**：测试环境 + 生产环境
- **滚动更新**：零停机部署更新

---

## 🚀 后续扩展方向

### 1. 代码质量管控流程化

#### 1.1 评分驱动的质量管控
- **质量门禁**：设定代码评分阈值（如80分以下），自动阻止合并
- **分级处理机制**：
  - 🔴 **严重问题（<60分）**：强制修复后才能合并
  - 🟡 **一般问题（60-79分）**：需要团队Leader审核确认
  - 🟢 **良好代码（≥80分）**：自动通过，快速合并

#### 1.2 问题跟踪与闭环管理
- **问题标记系统**：自动为发现的问题创建GitLab Issue
- **修复验证**：二次Review验证问题是否已解决
- **统计报表**：团队/个人代码质量趋势分析和改进建议

### 2. 多端技术栈全覆盖

#### 2.1 Native端接入支持
- **iOS项目支持**：
  - Swift/Objective-C代码审查
  - iOS开发规范自动加载
  - Xcode项目结构识别
- **Android项目支持**：
  - Kotlin/Java代码审查
  - Android开发规范集成
  - Gradle构建脚本检查

#### 2.2 跨平台项目支持
- **Flutter项目**：Dart语言支持，跨平台开发规范
- **React Native**：JavaScript/TypeScript混合项目审查
- **小程序项目**：微信/支付宝小程序代码规范

### 3. 智能化代码质量提升

#### 3.1 AI辅助代码优化
- **自动修复建议**：基于常见问题模式，提供具体的代码修复建议
- **重构建议**：识别代码异味，提供重构方案
- **性能优化提示**：检测潜在性能瓶颈，给出优化建议

#### 3.2 学习型审查系统
- **团队知识库**：积累团队常见问题和最佳实践
- **个性化审查**：基于开发者历史代码风格，提供个性化建议
- **规范演进**：根据审查数据，持续优化团队代码规范

### 4. 企业级功能增强

#### 4.1 权限与安全管控
- **分级权限管理**：不同角色的审查权限和配置权限
- **敏感信息检测**：自动识别和警告敏感信息泄露
- **合规性检查**：集成企业安全规范和合规要求

#### 4.2 数据分析与洞察
- **代码质量大盘**：团队整体代码质量趋势和热力图
- **开发效能分析**：代码Review效率和质量关联分析
- **技术债务管理**：识别和量化技术债务，制定偿还计划

### 5. 开发体验优化

#### 5.1 IDE集成
- **VSCode插件**：本地代码实时审查和建议
- **IntelliJ IDEA插件**：Java/Kotlin开发环境集成
- **Xcode扩展**：iOS开发环境原生支持

#### 5.2 CI/CD深度集成
- **Pipeline集成**：在CI/CD流水线中嵌入代码质量检查
- **自动化测试关联**：结合单元测试覆盖率进行综合评分
- **部署门禁**：基于代码质量评分控制部署流程

### 6. 团队协作增强

#### 6.1 Code Review文化建设
- **最佳实践分享**：定期推送优秀代码案例和改进建议
- **技能成长跟踪**：个人代码质量成长轨迹和技能图谱
- **团队竞赛机制**：代码质量排行榜和改进激励机制

#### 6.2 知识沉淀与传承
- **问题知识库**：常见问题的解决方案和最佳实践
- **新人培训**：基于历史审查数据的新人代码规范培训
- **技术分享**：自动生成技术分享内容和改进建议

---

## 📈 项目价值与展望

### 短期价值（3-6个月）
- **代码质量显著提升**：通过自动化审查，减少线上bug率20-30%
- **Review效率提升**：人工Review时间减少50%，专注于业务逻辑审查
- **团队协作优化**：通过飞书智能通知，提升问题响应速度

### 中期价值（6-12个月）
- **质量管控体系化**：建立完整的代码质量评分和管控流程
- **多端项目全覆盖**：前端、后端、Native端统一的代码质量标准
- **开发效能提升**：通过智能化建议，提升整体开发效率

### 长期价值（1年以上）
- **技术债务可控**：通过持续监控和改进，保持代码库健康度
- **团队技能提升**：通过AI辅助学习，提升团队整体技术水平
- **企业级质量体系**：建立行业领先的代码质量管控体系

---

## 📞 联系我们

如有任何问题或建议，欢迎联系技术团队：
- **项目地址**：https://gitlab.yc345.tv/ios/tools/AI-CodeReview
- **技术支持**：通过GitLab Issue提交问题
- **使用文档**：项目README.md

让我们一起用AI的力量，提升代码质量，加速团队协作！🎉
